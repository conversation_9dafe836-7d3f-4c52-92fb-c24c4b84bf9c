<template>
	<div class="main-layout">
		<!-- 内容区 -->
		<main class="main-layout-main">
			<slot name="default"></slot>
		</main>

		<div class="main-layout-overlay" id="main-layout-overlay">
			<!-- 添加上方背景渐变 -->
			<!-- <div class="main-layout-gradient main-layout-gradient-top"></div> -->
			<!-- 添加左侧背景渐变 -->
			<!-- <div class="main-layout-gradient main-layout-gradient-left"></div> -->
			<!-- 添加右侧背景渐变 -->
			<!-- <div class="main-layout-gradient main-layout-gradient-right"></div> -->
			<!-- 添加底部背景渐变 -->
			<!-- <div class="main-layout-gradient main-layout-gradient-bottom"></div> -->

			<div class="main-layout-header">
				<slot name="header"></slot>
			</div>

			<div class="main-layout-sides">
				<!-- 左侧边栏 -->
				<div
					v-if="showLeftSidebar"
					class="main-layout-sidebar main-layout-sidebar-left"
					:class="{ 'is-collapsed': leftCollapsed }"
					:style="leftCollapsed ? { transform: 'translateX(-100%)' } : {}"
				>
					<div class="main-layout-sidebar-content">
						<slot name="left-sidebar"> </slot>
					</div>
					<!-- <div
						v-if="showLeftToggle"
						class="main-layout-sidebar-toggle chevrons"
						@click="toggleLeftSidebar"
					>
						<img
							src="@/assets/images/people/chevrons-right.svg"
							:style="{
								transform: `rotate(${leftCollapsed ? '-180' : '0'}deg)`,
							}"
						/>
					</div> -->
					<!-- <div class="main-layout-sidebar-toggle" @click="toggleLeftSidebar">
						<img
							v-if="!leftCollapsed"
							src="@/assets/modules/common/icons/hide-btn-left.svg"
							alt="收起"
							class="toggle-icon-img"
						/>
						<img
							v-else
							src="@/assets/modules/common/icons/hide-btn-right.svg"
							alt="展开"
							class="toggle-icon-img"
						/>
					</div> -->
				</div>

				<!-- 右侧边栏 -->
				<div
					v-if="showRightSidebar"
					class="main-layout-sidebar main-layout-sidebar-right"
					:class="{ 'is-collapsed': rightCollapsed }"
					:style="rightCollapsed ? { transform: 'translateX(100%)' } : {}"
				>
					<div class="main-layout-sidebar-content">
						<slot name="right-sidebar"></slot>
					</div>
					<!-- 收起/展开按钮 -->
					<!-- <div class="main-layout-sidebar-toggle" @click="toggleRightSidebar">
						<img
							v-if="!rightCollapsed"
							src="@/assets/modules/common/icons/hide-btn-right.svg"
							alt="收起"
							class="toggle-icon-img"
						/>
						<img
							v-else
							src="@/assets/modules/common/icons/hide-btn-left.svg"
							alt="展开"
							class="toggle-icon-img"
						/>
					</div> -->
				</div>
			</div>

			<div class="main-layout-mid">
				<slot name="mid"></slot>
			</div>

			<div class="main-layout-footer">
				<slot name="footer"></slot>
			</div>

			<slot name="extra"></slot>
		</div>

		<div class="main-layout-bg"></div>
	</div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch } from "vue";
import autofit from "autofit.js";
import gsap from "gsap";

// 定义props，控制侧边栏显示隐藏
const props = defineProps({
	showLeftSidebar: {
		type: Boolean,
		default: true,
	},
	showRightSidebar: {
		type: Boolean,
		default: true,
	},
	showLeftToggle: {
		type: Boolean,
		default: false,
	},
});

// 侧边栏收起状态
const leftCollapsed = ref(false);
const rightCollapsed = ref(false);

// 通用侧边栏切换方法
const toggleSidebar = (side) => {
	const isLeft = side === "left";
	const stateRef = isLeft ? leftCollapsed : rightCollapsed;
	const sidebarClass = isLeft ? ".main-layout-sidebar-left" : ".main-layout-sidebar-right";

	stateRef.value = !stateRef.value;

	if (stateRef.value) {
		// 隐藏侧边栏内容
		gsap.to(`${sidebarClass} .main-layout-sidebar-content`, {
			duration: 0.5,
			// opacity: 0,
			ease: "power2.inOut",
		});
	} else {
		// 显示侧边栏内容
		gsap.to(`${sidebarClass} .main-layout-sidebar-content`, {
			duration: 0.5,
			// opacity: 1,
			ease: "power2.inOut",
		});
	}
};

// 切换左侧边栏
const toggleLeftSidebar = () => {
	toggleSidebar("left");
};

// 切换右侧边栏
const toggleRightSidebar = () => {
	toggleSidebar("right");
};

// 控制侧边栏状态的通用方法
const toggleSidebarState = (options = {}) => {
	const {
		side = "both", // 'left', 'right', 'both'
		state = "toggle", // 'open', 'close', 'toggle'
	} = options;

	const handleSide = (sideType) => {
		const stateRef = sideType === "left" ? leftCollapsed : rightCollapsed;
		const sidebarClass =
			sideType === "left" ? ".main-layout-sidebar-left" : ".main-layout-sidebar-right";

		// 根据指定状态设置侧边栏
		if (state === "open" && stateRef.value) {
			stateRef.value = false;
		} else if (state === "close" && !stateRef.value) {
			stateRef.value = true;
		} else if (state === "toggle") {
			stateRef.value = !stateRef.value;
		} else {
			// 如果当前状态已经是目标状态，则不做任何操作
			return;
		}

		// 应用动画效果
		gsap.to(`${sidebarClass} .main-layout-sidebar-content`, {
			duration: 0.5,
			ease: "power2.inOut",
		});
	};

	if (side === "both") {
		handleSide("left");
		handleSide("right");
	} else {
		handleSide(side);
	}
};

// autofit配置
let autofitInstance = null;

onMounted(() => {
	// 初始化主autofit
	autofitInstance = autofit.init({
		el: "body",
		dw: 1920,
		dh: 1080,
		resize: true,
	});
});

onUnmounted(() => {
	// 销毁主autofit实例
	if (autofitInstance) {
		autofitInstance.destroy();
	}
});

defineExpose({
	toggleLeftSidebar,
	toggleRightSidebar,
	toggleSidebarState,
});
</script>

<style lang="scss" scoped>
.main-layout {
	margin: 0 auto;
	position: relative;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	background-size: cover;
	&-gradient {
		position: absolute;
		pointer-events: none;
		z-index: 1;
		&-top {
			top: 0;
			left: 0;
			width: 100%;
			height: 186px;
			// 476px
			background: linear-gradient(180deg, #081935 0%, rgba(8, 25, 53, 0) 100%);
		}

		&-left {
			top: 0;
			left: 0;
			width: 400px;
			height: 100%;
			background: linear-gradient(
				269deg,
				rgba(8, 25, 53, 0) 5%,
				rgba(8, 25, 53, 0.49) 60%,
				#081935 100%
			);
		}

		&-right {
			top: 0;
			right: 0;
			width: 400px;
			height: 100%;
			background: linear-gradient(
				-269deg,
				rgba(8, 25, 53, 0) 5%,
				rgba(8, 25, 53, 0.49) 35%,
				#081935 100%
			);
		}

		&-bottom {
			bottom: 0;
			left: 0;
			width: 100%;
			height: 350px;
			background: linear-gradient(
				180deg,
				rgba(8, 25, 53, 0) 0%,
				rgba(8, 25, 53, 0.49) 48%,
				#081935 100%
			);
		}
	}

	&-main {
		position: relative;
		width: 100%;
		height: 100%;
		z-index: 2;
	}

	&-overlay {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 3;
		pointer-events: none;
		&:after {
			// 预留是否需要背景图
		}
	}

	&-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: var(--bg-url) no-repeat;
		background-size: cover;
		pointer-events: none;
		z-index: 1;
	}

	&-sides {
		.main-layout-sidebar {
			position: absolute;
			top: var(--sidebar-top);
			bottom: var(--sidebar-bottom);
			width: var(--sidebar-width);
			z-index: 3;
			pointer-events: auto;
			transition: transform 0.5s ease;

			&::after {
				content: "";
				position: absolute;
				top: 0;
				height: 100%;
				width: 3px;
				opacity: var(--after-opacity);
				background: linear-gradient(
					180deg,
					rgba(0, 116, 255, 0) 0%,
					#5ba5ff 50%,
					rgba(0, 116, 255, 0) 100%
				);
				border-radius: 0px 0px 0px 0px;
				transition: opacity 0.5s ease;
			}

			&-left,
			&-right {
				--after-opacity: 0; /* 默认隐藏边框 */
			}

			&-left {
				&::after {
					right: -20px; // 左侧区域显示右边框
				}
			}
			&-right {
				right: 0;
				&::after {
					left: -5px; // 右侧区域显示左边框
				}

				.main-layout-sidebar-toggle {
					left: -27px;
					right: auto;
				}
			}

			&.is-collapsed {
				&::after {
					opacity: 0;
				}
			}

			&-content {
				position: relative;
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;
				justify-content: space-between;
				// > * {
				//   width: 100%;

				//   flex: 1;
				// }
			}

			&-left {
			}

			&-right {
				right: 0;

				.main-layout-sidebar-toggle {
					left: -27px;
				}
			}

			&-toggle {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				right: -27px;
				width: 20px;
				height: 38px;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				pointer-events: auto;
				z-index: 10;
				transition: right 0.5s ease, left 0.5s ease;

				&.chevrons {
					top: 0;
					right: -50px;
					width: 32px;
					height: 32px;
					transform: unset;
					background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
						radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
						rgba(0, 138, 255, 0.3);
					box-shadow: inset 0px 0px 8px 0px rgba(0, 138, 255, 0.25);
					border: 1px solid;
					border-radius: 0 4px 4px 0;
					border-image: linear-gradient(
							90deg,
							rgba(0, 138, 255, 0),
							rgba(0, 138, 255, 1),
							rgba(0, 138, 255, 0.2),
							rgba(0, 138, 255, 0)
						)
						1 1;
				}
			}
		}

		.toggle-icon-img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}

	// 边缘展开按钮样式
	&-edge-toggle {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: -27px;
		width: 20px;
		height: 38px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		pointer-events: auto;
		z-index: 10;

		&-left {
			left: 0;
		}

		&-right {
			right: 0;
		}

		.toggle-icon-img {
			&-reverse {
				transform: rotate(180deg);
			}
		}
	}

	&-mid {
		position: absolute;
		top: 112px;
		bottom: 24px;
		width: 100%;
		height: calc(100% - 112px - 24px);
		pointer-events: auto;
		z-index: 2;

		&:empty {
			display: none;
		}
	}

	&-footer {
		position: absolute;
		bottom: 0px;
		left: 50%;
		transform: translateX(-50%);
		pointer-events: auto;
		z-index: 3;
	}

	:deep(.arco-spin) {
		width: 100%;
		height: 100%;
		.arco-spin-mask {
			background: rgba(19, 51, 92, 0.3); /* 降低不透明度以便看到模糊效果 */
			// backdrop-filter: blur(8px); /* 添加8px的模糊效果 */
		}
	}
}

// 一图统管模式下显示边框
.oneMap {
	.main-layout-sidebar {
		&-left,
		&-right {
			--after-opacity: 0.7; /* 一图统管模式下显示边框 */
		}
	}
}

// 党建模式下隐藏边框
.PartyBuilding {
	.main-layout-sidebar {
		&-left,
		&-right {
			--after-opacity: 0 !important; /* 党建模式下隐藏边框 */

			&::after {
				display: none !important; /* 完全隐藏伪元素 */
			}
		}
	}
}
</style>
