<template>
  <Modal ref="modalRef" title="形象进度视频" :icon="modelIconTitle" width="1200px">
    <div class="video-modal-content">
      <!-- 分屏选择器 -->
      <div class="screen-selector">
        <div
          v-for="screen in screenOptions"
          :key="screen.key"
          class="screen-option"
          :class="{ active: activeScreen === screen.key }"
          @click="handleScreenChange(screen.key)"
        >
          <div class="screen-icon">
            <img :src="screen.icon" :alt="screen.label" class="screen-icon-img" />
          </div>
          <span class="screen-label">{{ screen.label }}</span>
        </div>
      </div>

      <!-- 视频播放区域 -->
      <div class="video-player-area">
        <div class="video-grid" :class="getVideoGridClass()">
          <div
            v-for="(player, index) in getVideoPlayers()"
            :key="index"
            class="video-player"
            :class="{
              active: selectedPlayer === index,
              'has-video': player.videoId,
            }"
            @click="selectPlayer(index)"
          >
            <div v-if="player.videoId" class="video-content">
              <video
                :src="getVideoById(player.videoId)?.url"
                :poster="getVideoById(player.videoId)?.thumbnail"
                controls
                class="video-element"
              >
                您的浏览器不支持视频播放
              </video>
              <div class="video-info">
                <div class="video-title">{{ getVideoById(player.videoId)?.title }}</div>
                <div class="video-date">{{ getVideoById(player.videoId)?.date }}</div>
              </div>
            </div>
            <div v-else class="video-placeholder">
              <div class="placeholder-icon">
              </div>
              <div class="placeholder-text">
                 暂无视频源<br></br>
              选中此区域，点击下方视频进度视频投放
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频列表区域 -->
      <div class="video-list-area">
        <div class="list-header">
          <div class="list-title">形象进度视频记录</div>
          <div class="list-filters">
            <div class="filter-item">
              <div class="label">区域:</div>
              <a-select v-model="selectedRegion" placeholder="选择区域" allow-clear>
                <a-option value="格贡一">格贡一</a-option>
                <a-option value="格贡二">格贡二</a-option>
                <a-option value="格贡三">格贡三</a-option>
              </a-select>
            </div>
            <div class="filter-item">
              <div class="label">日期:</div>
              <a-date-picker v-model="selectedDate" :max="getCurrentDate()" />
            </div>
          </div>
        </div>

        <div class="video-list">
          <div
            v-for="video in filteredVideoList"
            :key="video.id"
            class="video-item"
            :class="{ selected: isVideoSelected(video.id) }"
            @click="assignVideoToPlayer(video)"
          >
            <div class="video-thumbnail">
              <img :src="video.thumbnail" :alt="video.title" />
              <!-- <div class="video-duration">{{ video.duration }}</div> -->
              <!-- 投放中标识 -->
              <div v-if="isVideoSelected(video.id)" class="casting-indicator">
                投放中
              </div>
            </div>
            <div class="video-details">
              <div class="video-title">{{ video.title }}</div>
              <div class="video-meta">{{ video.date }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import modelIconTitle from "@/assets/modules/traffic/icon/model-icon-title.svg?component";
import Modal from "@/views/gdMap/components/traffic/components/Modal.vue";

const modalRef = ref(null);

// 视频弹窗相关数据
const activeScreen = ref("1x1");
const selectedPlayer = ref(0);
const selectedRegion = ref("");
const selectedDate = ref("");

// 分屏选项
const screenOptions = [
  {
    key: "1x1",
    label: "1*1",
    count: 1,
    gridClass: "grid-1x1",
    icon: "/src/assets/images/detail/video-one.png",
  },
  {
    key: "1x2",
    label: "1*2",
    count: 2,
    gridClass: "grid-1x2",
    icon: "/src/assets/images/detail/video-two.png",
  },
  {
    key: "1x3",
    label: "1*3",
    count: 3,
    gridClass: "grid-1x3",
    icon: "/src/assets/images/detail/video-three.png",
  },
  {
    key: "2x2",
    label: "2*2",
    count: 4,
    gridClass: "grid-2x2",
    icon: "/src/assets/images/detail/video-four.png",
  },
  {
    key: "2x3",
    label: "2*3",
    count: 6,
    gridClass: "grid-2x3",
    icon: "/src/assets/images/detail/video-six.png",
  },
];

// 视频播放器数据
const videoPlayers = ref([
  { videoId: null },
  { videoId: null },
  { videoId: null },
  { videoId: null },
  { videoId: null },
  { videoId: null },
  { videoId: null },
  { videoId: null },
  { videoId: null },
]);

// 静态视频数据
const videoList = [
  {
    id: 1,
    title: "格贡一分部一工区",
    region: "格贡一",
    date: "2025-07",
    duration: "34:54",
    thumbnail:
      "https://s1.aigei.com/prevfiles/ea2e28935ad54d4e9e86167e42af1773.jpg?e=2051020800&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:8FhhIIaGcxAQLoVWX1bs-chkok4=",
    url: "https://www.w3schools.com/html/mov_bbb.mp4",
  },
  {
    id: 2,
    title: "格贡一分部二工区",
    region: "格贡一",
    date: "2025-07",
    duration: "28:32",
    thumbnail:
      "https://s1.aigei.com/prevfiles/ea2e28935ad54d4e9e86167e42af1773.jpg?e=2051020800&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:8FhhIIaGcxAQLoVWX1bs-chkok4=",
    url: "https://www.w3schools.com/html/movie.mp4",
  },
  {
    id: 3,
    title: "格贡二分部一工区",
    region: "格贡二",
    date: "2025-07",
    duration: "42:18",
    thumbnail:
      "https://s1.aigei.com/prevfiles/ea2e28935ad54d4e9e86167e42af1773.jpg?e=2051020800&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:8FhhIIaGcxAQLoVWX1bs-chkok4=",
    url: "https://www.w3schools.com/html/mov_bbb.mp4",
  },
  {
    id: 4,
    title: "格贡二分部二工区",
    region: "格贡二",
    date: "2025-07",
    duration: "35:27",
    thumbnail:
      "https://s1.aigei.com/prevfiles/ea2e28935ad54d4e9e86167e42af1773.jpg?e=2051020800&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:8FhhIIaGcxAQLoVWX1bs-chkok4=",
    url: "https://www.w3schools.com/html/movie.mp4",
  },
  {
    id: 5,
    title: "格贡三分部一工区",
    region: "格贡三",
    date: "2025-07",
    duration: "39:15",
    thumbnail:
      "https://s1.aigei.com/prevfiles/ea2e28935ad54d4e9e86167e42af1773.jpg?e=2051020800&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:8FhhIIaGcxAQLoVWX1bs-chkok4=",
    url: "https://www.w3schools.com/html/mov_bbb.mp4",
  },
  {
    id: 6,
    title: "格贡三分部二工区",
    region: "格贡三",
    date: "2025-07",
    duration: "31:48",
    thumbnail:
      "https://s1.aigei.com/prevfiles/ea2e28935ad54d4e9e86167e42af1773.jpg?e=2051020800&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:8FhhIIaGcxAQLoVWX1bs-chkok4=",
    url: "https://www.w3schools.com/html/movie.mp4",
  },
];

// 视频弹窗相关方法
const handleScreenChange = (screenKey) => {
  activeScreen.value = screenKey;
  selectedPlayer.value = 0; // 重置选中的播放器
};

const getVideoGridClass = () => {
  const screen = screenOptions.find((s) => s.key === activeScreen.value);
  return screen ? screen.gridClass : "grid-1x1";
};

const getVideoPlayers = () => {
  const screen = screenOptions.find((s) => s.key === activeScreen.value);
  const count = screen ? screen.count : 1;
  return videoPlayers.value.slice(0, count);
};

const selectPlayer = (index) => {
  selectedPlayer.value = index;
};

const getVideoById = (videoId) => {
  return videoList.find((video) => video.id === videoId);
};

const assignVideoToPlayer = (video) => {
  if (selectedPlayer.value !== null) {
    videoPlayers.value[selectedPlayer.value].videoId = video.id;
  }
};

const isVideoSelected = (videoId) => {
  return videoPlayers.value.some((player) => player.videoId === videoId);
};

const getCurrentDate = () => {
  return new Date();
};

// 过滤视频列表
const filteredVideoList = computed(() => {
  return videoList.filter((video) => {
    const regionMatch = !selectedRegion.value || video.region === selectedRegion.value;

    let dateMatch = true;
    if (selectedDate.value) {
      // 将Arco Design的日期对象转换为YYYY-MM格式进行匹配
      const selectedDateStr = selectedDate.value.toISOString().substring(0, 7);
      dateMatch = video.date.includes(selectedDateStr);
    }

    return regionMatch && dateMatch;
  });
});

// 打开弹窗
const open = () => {
  modalRef.value?.open();
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
/* 视频弹窗样式 */
.video-modal-content {
  min-height: 600px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  /* background: #001220; */
}

/* 分屏选择器样式 */
.screen-selector {
  display: flex;
  gap: 20px;
  justify-content: flex-end;
  /* padding: 15px; */
}

.screen-option {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  padding: 8px 15px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 80px;

  &:hover {
    background: rgba(0, 138, 255, 0.2);
    border-color: rgba(0, 138, 255, 0.5);
  }

  &.active {
    background: rgba(0, 138, 255, 0.3);
    border-color: #008aff;
    box-shadow: 0 0 10px rgba(0, 138, 255, 0.3);
  }
}

.screen-icon {
  width: 24px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.screen-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.screen-label {
  font-size: 13px;
  color: #c4e5ff;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  white-space: nowrap;
}

/* 视频播放区域样式 */
.video-player-area {
  flex-shrink: 0;
  /* padding: 15px; */
  /* background: rgba(0, 0, 0, 0.3); */
  /* border-radius: 8px; */
  /* border: 1px solid rgba(0, 138, 255, 0.2); */
}

.video-grid {
  display: grid;
  gap: 10px;
  min-height: 300px;
  &.grid-1x1 {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }

  &.grid-1x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.grid-1x3 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr;
  }

  &.grid-2x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }

  &.grid-2x3 {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }
}

.video-player {
  position: relative;
  /* background: rgba(0, 0, 0, 0.5); */
  background-image: url("@/assets/images/detail/video-noselect.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  border-radius: 6px;
  /* border: 2px solid transparent; */
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 250px;



  &.active {
    background-image: url("@/assets/images/detail/video-selected.png");
    /* border-color: #008aff;
    box-shadow: 0 0 15px rgba(0, 138, 255, 0.4); */
  }
}

.video-content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.video-element {
  position: absolute;
  top: 6%;
  left: 4%;
  width: 92%;
  height: 70%;
  object-fit: cover;
}

.video-info {
  position: absolute;
  left: 3%;
  bottom: 0;
  height: 20%;
  width: 90%;
  padding: 8px 12px;
  color: white;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.video-title {
  font-size: 13px;
  font-weight: bold;
  font-family: Alibaba PuHuiTi;
  color: #c4e5ff;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 12px;
}

.video-date {
  font-size: 11px;
  opacity: 0.8;
  font-family: Alibaba PuHuiTi;
  color: rgba(196, 229, 255, 0.7);
  white-space: nowrap;
  flex-shrink: 0;
}

.video-placeholder {
  position: absolute;
  top: 6%;
  left: 4%;
  width: 92%;
  height: 70%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(196, 229, 255, 0.7);
  text-align: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  box-sizing: border-box;
}

.placeholder-icon {
  width: 40px;
  height: 40px;
background-image: url("@/assets/images/detail/<EMAIL>");
background-repeat: no-repeat;
background-size: contain;
  margin-bottom: 10px;
  opacity: 0.5;
}

.placeholder-text {
  color: #96aabd;
  font-size: 14px;
  line-height: 1.5;
  font-family: Alibaba PuHuiTi;
}

/* 视频列表区域样式 */
.video-list-area {
  background:rgba(8, 69, 121,0.4);
  /* border-radius: 8px; */
  border: 1px solid rgba(0, 138, 255, 0.2);
  padding: 15px;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 15px;
  padding-bottom: 10px; */
  flex-shrink: 0;
}

.list-title {
font-weight: normal;
font-size: 20px;
color: #FFFFFF;
letter-spacing: 2px;
text-align: left;
font-style: normal;
text-transform: none;
background: linear-gradient(90deg, #DFF0FA 19%, #E0F0FF 46%, #A8D4F9 82%);
background-clip: text;
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;

  font-family: Alibaba PuHuiTi;
}

.list-filters {
  display: flex;
  gap: 20px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .label {
    font-family: Alibaba PuHuiTi;
    font-size: 14px;
    color: #ffffff;
    white-space: nowrap;
  }

  :deep(.arco-select) {
    width: 120px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid #10477b;
    border-radius: 4px;
    background-color: #001120;

    .arco-select-view-single {
      height: 100%;
      background-color: #001120;
      border: none;
      color: rgba(255, 255, 255, 0.9);
      font-family: Alibaba PuHuiTi;
      font-size: 12px;
    }

    &.arco-select-focused {
      border-color: #008aff;
      box-shadow: 0 0 8px rgba(0, 138, 255, 0.3);
    }
  }

  :deep(.arco-picker) {
    width: 140px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid #10477b;
    border-radius: 4px;
    background-color: #001120;

    .arco-picker-input {
      background-color: #001120;
      border: none;
      color: rgba(255, 255, 255, 0.9);
      font-family: Alibaba PuHuiTi;
      font-size: 12px;
    }

    &.arco-picker-focused {
      border-color: #008aff;
      box-shadow: 0 0 8px rgba(0, 138, 255, 0.3);
    }
  }
}

.video-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  padding-top: 15px;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 18, 32, 0.3);
    border-radius: 4px;
    margin: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(0, 138, 255, 0.6) 0%, rgba(0, 138, 255, 0.8) 100%);
    border-radius: 4px;
    border: 1px solid rgba(0, 138, 255, 0.2);

    &:hover {
      background: linear-gradient(180deg, rgba(0, 138, 255, 0.8) 0%, rgba(0, 138, 255, 1) 100%);
      border-color: rgba(0, 138, 255, 0.4);
    }

    &:active {
      background: rgba(0, 138, 255, 1);
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

.video-item {
  display: flex;
  flex-direction: column;
  /* background: rgba(0, 18, 32, 0.5); */
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 180px;

  &:hover {
    transform: translateY(-2px);
  }
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 80%;
  flex-shrink: 0;
  background: #000;
  transition: all 0.3s ease;
  box-sizing: border-box;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .video-item.selected & {
    border: 3px solid #008aff;
    box-shadow: 0 0 15px rgba(0, 138, 255, 0.5);
  }
}

.video-duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 10px;
  padding: 3px 6px;
  border-radius: 3px;
  font-family: Alibaba PuHuiTi;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.casting-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 138, 255, 0.9);
  color: white;
background: linear-gradient( 180deg, rgba(0,138,255,0) 47%, #008AFF 100%), rgba(0,138,255,1);
border: 1px solid #70BCFF;
padding: 5px;
  z-index: 10;
  white-space: nowrap;
}

.video-details {
  height: 20%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  /* background:#054379;
  border-top: 1px solid rgba(0, 138, 255, 0.3); */
}

.video-title {
  font-size: 13px;
  font-weight: bold;
  color: #c4e5ff;
  font-family: Alibaba PuHuiTi;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 12px;
}

.video-meta {
  font-size: 11px;
  color: rgba(196, 229, 255, 0.7);
  font-family: Alibaba PuHuiTi;
  white-space: nowrap;
  flex-shrink: 0;
}
</style>

<style>
/* Arco Design 组件全局样式覆盖 */
.arco-select-dropdown {
  background-color: #001120 !important;
  border: 1px solid #10477b !important;
  border-radius: 4px !important;
}

.arco-select-option {
  background-color: #001120 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-family: Alibaba PuHuiTi !important;
  font-size: 12px !important;
}

.arco-select-option:hover {
  background-color: rgba(0, 138, 255, 0.2) !important;
}

.arco-select-option.arco-select-option-selected {
  background-color: rgba(0, 138, 255, 0.3) !important;
  color: #ffffff !important;
}

.arco-picker-dropdown {
  background-color: #001120 !important;
  border: 1px solid #10477b !important;
  border-radius: 4px !important;
}

.arco-picker-cell {
  color: rgba(255, 255, 255, 0.9) !important;
}

.arco-picker-cell:hover {
  background-color: rgba(0, 138, 255, 0.2) !important;
}

.arco-picker-cell.arco-picker-cell-selected {
  background-color: rgba(0, 138, 255, 0.4) !important;
  color: #ffffff !important;
}

.arco-picker-header {
  border-bottom: 1px solid #10477b !important;
}

.arco-picker-header-icon {
  color: rgba(255, 255, 255, 0.7) !important;
}

.arco-picker-header-value {
  color: rgba(255, 255, 255, 0.9) !important;
  font-family: Alibaba PuHuiTi !important;
}
</style>
