<template>
  <div class="container">
    <div class="side">
      <div class="headline">
        <div class="headline-main">青藏公路大事记</div>
        <div class="headline-translation">Memoirs of Smart Highways</div>
      </div>
    </div>
    <div class="main">
      <div class="header">
        <div class="item">
          <div class="label">时间:</div>
          <a-radio-group v-model="params.dateType" type="button">
            <a-radio value="year">年</a-radio>
            <a-radio value="month">月</a-radio>
            <a-radio value="day">日</a-radio>
          </a-radio-group>
          <template v-if="params.dateType === 'year'">
            <a-year-picker v-model="params.date" :trigger-props="triggerProps" />
          </template>
          <template v-else-if="params.dateType === 'month'">
            <a-month-picker v-model="params.date" :trigger-props="triggerProps" />
          </template>
          <template v-else-if="params.dateType === 'day'">
            <a-date-picker v-model="params.date" :trigger-props="triggerProps" />
          </template>
        </div>
        <div class="item">
          <div class="label">所属路段:</div>
          <a-input v-model="params.tags"></a-input>
        </div>
        <div class="item">
          <div class="label">关键字:</div>
          <a-input v-model="params.keyword"></a-input>
        </div>
        <a-button class="search" @click="onSearch">搜索</a-button>
        <a-button class="reset" @click="onReset">重置</a-button>
      </div>
      <div class="content">
        <a-scrollbar type="track">
          <a-timeline direction="horizontal" mode="bottom">
            <a-timeline-item v-for="(node, index) in data" :key="node.day" label="2012-08">
              <template #label>
                <div class="card-container" :class="{ 'is-since': isSince(index) }">
                  <div v-if="isSince(index)" class="card-sign">
                    <div class="card-sign-label">Since</div>
                  </div>
                  <div class="line"></div>
                  <div class="card-content">
                    <div
                      v-for="item in node.array"
                      :key="item.id"
                      class="card-item"
                      @click="onClick(item)"
                    >
                      <div class="card-item-time">
                        {{ item.publishDate }}
                      </div>
                      <div class="card-item-desc">
                        {{ item.content }}
                      </div>
                      <div v-if="index !== 0" class="card-item-tag">
                        {{ item.tags }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template #dot>
                <div class="dot" :class="{ 'is-since': isSince(index) }">
                  {{ node.day }}
                </div>
              </template>
            </a-timeline-item>
          </a-timeline>
        </a-scrollbar>
        <timelineBg class="timeline-bg" />
      </div>
      <div class="footer">
        <div class="publicity">
          <div class="publicity-label">基本介绍</div>
          <div class="publicity-content">
            <video
              v-if="firstVideo"
              :src="firstVideo"
              autoplay
              muted
              loop
              controls
              style="width: 316px; height: 108px; object-fit: cover"
            />
            <img v-else src="@/assets/modules/common/bg/home-bg.webp" />
            <div class="publicity-content-desc">
              青藏公路 G10X
              线格尔木至那曲段提质改造工程起于青海省格尔木市区西南侧原西藏公路局青藏公路分局管辖起点，终点位于那曲市南
              G10X 线与迎宾大道交汇处，路线长度约 815km，总投资约101.9亿元，实施模式为EPC+。
            </div>
            <div class="publicity-content-button" @click="onClickPublicity">查看详情</div>
          </div>
        </div>
      </div>
    </div>
    <div class="mask"></div>
  </div>
  <DetailModal ref="DetailModalRef" />
</template>

<script setup>
import dayjs from "dayjs";
import request from "@/utils/request";
import timelineBg from "@/assets/modules/dashiji/bg/timeline-bg.svg?component";
import DetailModal from "./components/DetailModal.vue";
import { cloneDeep } from "lodash-es";

const triggerProps = {
  "content-class": "DSJDatePicker",
};
const originalParams = {
  dateType: "year",
  date: "",
  tags: "",
  keyword: "",
};
const params = ref(cloneDeep(originalParams));
const data = ref([]);
const searchFlag = ref(false);
const DetailModalRef = ref(null);
const firstVideo = ref("");

// 获取工艺模拟视频
const getSimulationVideo = () => {
  request
    .get("/api/screen/board/right/simulation/list")
    .then(async (res) => {
      if (res.data && res.data.length > 0) {
        // 获取第一个视频的下载链接
        const firstVideoPath = res.data[0];
        const { data } = await request.get("/api/upload-histories/file/download/preurl", {
          path: firstVideoPath,
        });
        firstVideo.value = data;
      }
    })
    .catch((error) => {
      console.error("获取工艺模拟视频失败:", error);
    });
};

onMounted(() => {
  getData();
  getSimulationVideo();
});

const getData = () => {
  request.get("/api/screen/dashiji/list", { ...unref(params) }).then((res) => {
    if (res.code == 200) {
      const map = new Map();

      res.data.forEach((item) => {
        const day = dayjs(item.publishDate).format("YYYY-MM-DD");
        const array = map.get(day) || [];
        array.push(item);
        map.set(day, array);
      });

      const result = [];
      [...map.keys()]
        .sort()
        .reverse()
        .forEach((day) => {
          const array = map.get(day).sort((a, b) => {
            if (a.publishDate === b.publishDate) return a.orderId - b.orderId;
            else return new Date(a.publishDate) - new Date(b.publishDate);
          });
          result.push({
            day,
            array,
          });
        });

      data.value = result;
    }
  });
};

const onSearch = () => {
  searchFlag.value = true;
  getData();
};

const onReset = () => {
  searchFlag.value = false;
  params.value = cloneDeep(originalParams);
  getData();
};

const onClick = (item) => {
  DetailModalRef.value.open(item);
};

const onClickPublicity = () => {
  // onClick({});
};

const isSince = (index) => {
  return index === data.value.length - 1 && !searchFlag.value;
};
</script>

<style lang="scss" scoped>
.container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
}

.side {
  width: 146px;
  height: 100%;
  z-index: 1;

  .headline {
    width: 114px;
    height: 732px;
    background-image: url("@/assets/modules/dashiji/bg/headline-bg.webp");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-left: 32px;
    margin-top: 214px;
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 8px;

    &-main {
      width: fit-content;
      height: fit-content;
      font-family: Alibaba PuHuiTi;
      font-size: 40px;
      background-image: linear-gradient(360deg, #ffffff 0%, #29a9ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      writing-mode: vertical-rl;
    }

    &-translation {
      font-family: Alibaba PuHuiTi;
      font-size: 20px;
      color: #72c6ff;
      writing-mode: sideways-lr;
    }
  }
}

.main {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-left: 24px;
  z-index: 1;
  overflow: hidden;
}

.header {
  margin-top: 100px;
  display: flex;
  align-items: center;
  column-gap: 16px;

  .item {
    display: flex;
    align-items: center;
    column-gap: 16px;
  }

  .label {
    flex-shrink: 0;
    font-family: Alibaba PuHuiTi;
    font-size: 14px;
    color: #ffffff;
  }

  :deep(.arco-radio-group-button) {
    height: 32px;
    padding: 0;
    box-sizing: border-box;
    border: 1px solid #10477b;
    border-radius: 4px;
    overflow: hidden;

    .arco-radio-button {
      height: 100%;
      margin: 0;
      border-radius: 0;
      background-color: #001120;

      &::before {
        display: none;
      }

      &:not(:first-child) {
        border-left: 1px solid #10477b;
      }

      .arco-radio-button-content {
        height: 100%;
        padding: 0 8px;
        font-family: Alibaba PuHuiTi;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
      }

      &.arco-radio-checked {
        .arco-radio-button-content {
          color: rgb(var(--primary-6));
        }
      }
    }
  }

  :deep(.arco-picker) {
    width: 280px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid #10477b;
    border-radius: 4px;
    background-color: #001120;
  }

  :deep(.arco-input-wrapper) {
    width: 280px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid #10477b;
    border-radius: 4px;
    background-color: #001120;
  }

  :deep(.arco-btn) {
    width: 40px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid #10477b;
    border-radius: 4px;
    background-color: #001120;

    &.search {
      background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
        radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
        rgba(0, 138, 255, 0.3);
      box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
      border-image: linear-gradient(
          90deg,
          rgba(0, 138, 255, 0),
          rgba(0, 138, 255, 1),
          rgba(0, 138, 255, 0.2),
          rgba(0, 138, 255, 0)
        )
        1 1;
    }
  }
}

.content {
  margin-top: 32px;
  margin-bottom: 24px;
  flex: 1;
  position: relative;
  // overflow: hidden;

  :deep(.arco-scrollbar) {
    width: 100%;
    height: 100%;
    z-index: 1;

    .arco-scrollbar-container {
      width: 100%;
      height: 100%;
      overflow: auto;
    }

    .arco-scrollbar-track {
      height: 12px;
      background: #091b2b;
      border: none;

      .arco-scrollbar-thumb {
        height: 100%;

        .arco-scrollbar-thumb-bar {
          height: 100%;
          margin: 0;
          border-radius: 6px;
          background: linear-gradient(90deg, #00d2ff 0%, rgba(0, 210, 255, 0) 100%), #3168ff;
        }
      }
    }
  }

  :deep(.arco-timeline) {
    width: fit-content;
    height: calc(100% - 48px);
    padding-top: 12px;
    padding-left: 96px;
    align-items: end;
    column-gap: 48px;

    .arco-timeline-item {
      flex: unset;
      min-width: fit-content;
      height: fit-content;
      padding-right: 24px;
      max-height: 100%;
      .arco-timeline-item-dot-line {
        display: none;
      }

      .arco-timeline-item-dot-custom {
        background-color: transparent;
      }
    }
  }

  .card-container {
    padding-bottom: 24px;
    margin-left: 1.6px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: -4px;
      left: -3px;
      width: 8px;
      height: 8px;
      background: #004178;
      box-sizing: border-box;
      border-radius: 50%;
      border: 1px solid #ffffff;
    }

    .line {
      position: absolute;
      left: 0px;
      width: 2px;
      height: 100%;
      box-sizing: border-box;
      background-image: linear-gradient(180deg, rgba(0, 138, 255, 1), rgba(0, 138, 255, 0));
    }

    .card-sign {
      position: absolute;
      top: -36px;
      left: 24px;
      width: 103px;
      height: 24px;
      background-image: url("@/assets/modules/dashiji/icon/card-sign-since.svg");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      &-label {
        position: absolute;
        left: 16px;
        top: -12px;
        font-family: D-DIN-PRO;
        font-weight: bold;
        font-size: 20px;
        color: #ffffff;
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      max-height: 680px;
    }

    .card-item {
      width: 204px;
      height: 138px;
      background-image: url("@/assets/modules/dashiji/bg/card-bg.svg");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-bottom: 24px;
      margin-left: 24px;
      cursor: pointer;

      &-time {
        height: 34px;
        font-family: D-DIN-PRO;
        font-weight: bold;
        font-size: 20px;
        color: #ffffff;
        padding: 1px 24px;
      }

      &-desc {
        font-family: Alibaba PuHuiTi;
        font-size: 12px;
        color: #ffffff;
        margin: 4px 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        line-height: 23px;
        height: 69px;
      }

      &-tag {
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Alibaba PuHuiTi;
        font-size: 12px;
        color: #ffffff;
      }
    }

    &.is-since {
      &::after {
        background: #ff9325;
      }

      .line {
        background-image: linear-gradient(180deg, rgba(255, 147, 37, 1), rgba(255, 147, 37, 0));
      }

      .card-item {
        position: relative;
        background: linear-gradient(
          90deg,
          rgba(255, 147, 37, 0.3) 0%,
          rgba(255, 147, 37, 0.1) 100%
        );
        border-radius: 12px;
        overflow: hidden;

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 12px;
          padding: 1.6px;
          background: linear-gradient(270deg, rgba(255, 147, 37, 0), rgba(255, 147, 37, 1));
          -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          z-index: -1;
        }

        &-time {
          height: 32px;
          padding-left: 12px;
          background-image: linear-gradient(90deg, #ffffff 0%, #ff9325 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        &-desc {
          margin-top: 0;
          color: #feae5c;
          -webkit-line-clamp: 4;
          height: 92px;
        }
      }
    }
  }

  .dot {
    font-family: D-DIN-PRO;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    position: relative;
    white-space: nowrap;

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      top: -32px;
      transform: translateX(-50%);
      width: 24px;
      height: 24px;
      background-image: url("@/assets/modules/dashiji/icon/timeline-dot.svg");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    &.is-since {
      color: #feae5c;

      &::after {
        background-image: url("@/assets/modules/dashiji/icon/timeline-dot-since.svg");
      }
    }
  }

  .timeline-bg {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 12px;
    z-index: 0;
  }
}

.footer {
  height: 124px;
  margin-bottom: 32px;

  .publicity {
    display: flex;
    align-items: center;
    column-gap: 20px;

    &-label {
      font-family: Alibaba PuHuiTi;
      font-size: 20px;
      background-image: linear-gradient(90deg, #ffffff 0%, #29a9ff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      writing-mode: vertical-rl;
    }

    &-content {
      width: 1662px;
      height: 124px;
      background-image: url("@/assets/modules/dashiji/bg/publicity-content-bg.svg");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding: 8px 0 8px 8px;
      display: flex;
      align-items: center;

      img {
        width: 316px;
        height: 108px;
      }

      &-desc {
        flex: 1;
        padding: 0 8px;

        font-family: Alibaba PuHuiTi;
        font-size: 20px;
        color: #ffffff;
        line-height: 36px;
        background-image: linear-gradient(180deg, #edfbff 0%, #008aff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      &-button {
        width: 88px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Alibaba PuHuiTi;
        font-size: 12px;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
}

.mask {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  backdrop-filter: blur(8px);
  background-color: var(--color-mask-bg);
  z-index: 0;
}
</style>

<style>
.DSJDatePicker {
  &.arco-trigger-content {
    .arco-picker-container {
      border-color: #008aff;
      background-color: #001120;
    }

    .arco-picker-header {
      border-color: #008aff;
    }

    .arco-picker-footer-now-wrapper {
      border-color: #008aff;
    }

    .arco-picker-header-icon {
      background: transparent;

      &.arco-picker-header-icon-hidden {
        display: none;
      }
    }
  }
}
</style>
